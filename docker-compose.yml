version: '3.8'

services:
  # AutoDoc API service
  autodoc-api:
    build:
      context: .
      dockerfile: Dockerfile
      args:
        BUILD_DATE: ${BUILD_DATE:-$(date -u +'%Y-%m-%dT%H:%M:%SZ')}
        VERSION: ${VERSION:-2.0.0}
    container_name: autodoc-api
    ports:
      - "8000:8000"
    environment:
      - ENVIRONMENT=docker
      - DEBUG=false
      - LOG_LEVEL=INFO
      - MONGODB_URL=mongodb://mongodb:27017
      - MONGODB_DATABASE=autodoc_docker
      - STORAGE_TYPE=local
      - STORAGE_BASE_PATH=/app/data
    volumes:
      - autodoc_data:/app/data
      - ./logs:/app/logs
    depends_on:
      - mongodb
    networks:
      - autodoc-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8000/health/"]
      interval: 30s
      timeout: 10s
      retries: 3

  # MongoDB Community Server
  mongodb:
    image: mongo:7.0-jammy
    container_name: autodoc-mongodb
    ports:
      - "27017:27017"
    environment:
      - MONGO_INITDB_ROOT_USERNAME=${MONGO_ROOT_USERNAME:-admin}
      - MONGO_INITDB_ROOT_PASSWORD=${MONGO_ROOT_PASSWORD:-password}
      - MONGO_INITDB_DATABASE=autodoc_docker
    volumes:
      - mongodb_data:/data/db
      - mongodb_config:/data/configdb
    networks:
      - autodoc-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "mongosh", "--quiet", "--eval", "db.adminCommand('ping').ok"]
      interval: 30s
      timeout: 10s
      retries: 3
    command: ["mongod", "--auth"]

  # MongoDB Express (optional - for development)
  mongo-express:
    image: mongo-express:1.0.2
    container_name: autodoc-mongo-express
    ports:
      - "8081:8081"
    environment:
      - ME_CONFIG_MONGODB_ADMINUSERNAME=${MONGO_ROOT_USERNAME:-admin}
      - ME_CONFIG_MONGODB_ADMINPASSWORD=${MONGO_ROOT_PASSWORD:-password}
      - ME_CONFIG_MONGODB_SERVER=mongodb
      - ME_CONFIG_MONGODB_PORT=27017
      - ME_CONFIG_BASICAUTH_USERNAME=${MONGO_EXPRESS_USER:-admin}
      - ME_CONFIG_BASICAUTH_PASSWORD=${MONGO_EXPRESS_PASSWORD:-admin}
    depends_on:
      - mongodb
    networks:
      - autodoc-network
    restart: unless-stopped
    profiles:
      - dev

volumes:
  autodoc_data:
    driver: local
  mongodb_data:
    driver: local
  mongodb_config:
    driver: local

networks:
  autodoc-network:
    driver: bridge
