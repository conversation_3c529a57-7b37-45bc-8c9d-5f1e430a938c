# AutoDoc v2 Environment Configuration

# Application Settings
APP_NAME=AutoDoc v2
APP_VERSION=2.0.0
ENVIRONMENT=development
DEBUG=true
LOG_LEVEL=INFO

# API Configuration
API_HOST=0.0.0.0
API_PORT=8000
API_PREFIX=/api/v2
CORS_ORIGINS=http://localhost:3000,http://localhost:8080

# Database Configuration
MONGODB_URL=mongodb://localhost:27017
MONGODB_DATABASE=autodoc_dev
MONGODB_MAX_CONNECTIONS=10
MONGODB_MIN_CONNECTIONS=1

# Storage Configuration
STORAGE_TYPE=local
STORAGE_BASE_PATH=./data
AWS_REGION=us-east-1
AWS_S3_BUCKET=autodoc-storage
AWS_ACCESS_KEY_ID=your-access-key
AWS_SECRET_ACCESS_KEY=your-secret-key

# LLM Provider Configuration
OPENAI_API_KEY=your-openai-api-key
OPENAI_MODEL=gpt-4-turbo-preview
OPENAI_MAX_TOKENS=4000
OPENAI_TEMPERATURE=0.1

GOOGLE_API_KEY=your-google-api-key
GEMINI_MODEL=gemini-pro
GEMINI_MAX_TOKENS=4000
GEMINI_TEMPERATURE=0.1

AWS_BEDROCK_REGION=us-east-1
BEDROCK_MODEL_ID=anthropic.claude-3-sonnet-20240229-v1:0

OLLAMA_BASE_URL=http://localhost:11434
OLLAMA_MODEL=llama2

# Security Configuration
SECRET_KEY=your-super-secret-key-change-this-in-production
ALGORITHM=HS256
ACCESS_TOKEN_EXPIRE_MINUTES=30

# Webhook Configuration
WEBHOOK_SECRET_KEY=your-webhook-secret-key
GITHUB_WEBHOOK_SECRET=your-github-webhook-secret
BITBUCKET_WEBHOOK_SECRET=your-bitbucket-webhook-secret

# Performance Configuration
MAX_CONCURRENT_ANALYSES=3
ANALYSIS_TIMEOUT_MINUTES=30
EMBEDDING_BATCH_SIZE=100
CACHE_TTL_SECONDS=3600

# Repository Configuration
CLONE_TIMEOUT_SECONDS=300
MAX_FILE_SIZE_MB=10
SUPPORTED_LANGUAGES=python,javascript,typescript,java,go,rust,cpp,c,csharp,php,ruby

# Monitoring and Observability
ENABLE_METRICS=true
METRICS_PORT=8001
ENABLE_TRACING=false
JAEGER_ENDPOINT=http://localhost:14268/api/traces

# Development Settings
RELOAD=true
WORKERS=1
