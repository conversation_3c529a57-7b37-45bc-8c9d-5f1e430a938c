[flake8]
max-line-length = 88
extend-ignore = 
    # E203: whitespace before ':' (conflicts with black)
    E203,
    # W503: line break before binary operator (conflicts with black)
    W503,
    # E501: line too long (handled by black)
    E501
exclude = 
    .git,
    __pycache__,
    .venv,
    venv,
    .mypy_cache,
    .pytest_cache,
    build,
    dist,
    *.egg-info,
    migrations
per-file-ignores =
    # F401: imported but unused (common in __init__.py files)
    __init__.py:F401
    # F403: star import used (common in __init__.py files)
    __init__.py:F403
max-complexity = 10
import-order-style = google
application-import-names = src,tests
