
# Implementation Plan: AutoDoc — Intelligent Automated Documentation Partner

**Branch**: `001-build-the-autodoc` | **Date**: 2025-09-21 | **Spec**: [spec.md](./spec.md)
**Input**: Feature specification from `E:/projects/autodoc/autodoc-v2/specs/001-build-the-autodoc/spec.md`

## Execution Flow (/plan command scope)
```
1. Load feature spec from Input path
   → If not found: ERROR "No feature spec at {path}"
2. Fill Technical Context (scan for NEEDS CLARIFICATION)
   → Detect Project Type from context (web=frontend+backend, mobile=app+api)
   → Set Structure Decision based on project type
3. Fill the Constitution Check section based on the content of the constitution document.
4. Evaluate Constitution Check section below
   → If violations exist: Document in Complexity Tracking
   → If no justification possible: ERROR "Simplify approach first"
   → Update Progress Tracking: Initial Constitution Check
5. Execute Phase 0 → research.md
   → If NEEDS CLARIFICATION remain: ERROR "Resolve unknowns"
6. Execute Phase 1 → contracts, data-model.md, quickstart.md, agent-specific template file (e.g., `CLAUDE.md` for Claude Code, `.github/copilot-instructions.md` for GitHub Copilot, `GEMINI.md` for Gemini CLI, `QWEN.md` for Qwen Code or `AGENTS.md` for opencode).
7. Re-evaluate Constitution Check section
   → If new violations: Refactor design, return to Phase 1
   → Update Progress Tracking: Post-Design Constitution Check
8. Plan Phase 2 → Describe task generation approach (DO NOT create tasks.md)
9. STOP - Ready for /tasks command
```

**IMPORTANT**: The /plan command STOPS at step 7. Phases 2-4 are executed by other commands:
- Phase 2: /tasks command creates tasks.md
- Phase 3-4: Implementation execution (manual or via tools)

## Summary
AutoDoc v2 is a headless, API-first documentation generation system that processes Git repositories to create comprehensive technical documentation through AI-powered analysis. The system leverages LangGraph for workflow orchestration, FastAPI for high-performance APIs, and environment-adaptive storage (Local MongoDB for dev, S3/MongoDB for production). Core functionality includes deep code analysis, automated wiki generation with Mermaid diagrams, and conversational querying with provenance tracking.

## Technical Context
**Language/Version**: Python 3.12+ (async/await required for I/O operations)  
**Primary Dependencies**: FastAPI, LangGraph, LangChain, Pydantic, pymongo, boto3  
**Storage**: Environment-adaptive - Local filesystem + MongoDB (dev) / AWS S3 + MongoDB (prod)  
**Testing**: pytest with async support, contract testing for external integrations  
**Target Platform**: Linux server (AWS ECS), Docker containerized  
**Project Type**: single (headless API service, no frontend components)  
**Performance Goals**: p50 ≤ 500ms API responses, p95 ≤ 1500ms, streaming chat ≤ 1500ms first token  
**Constraints**: <1% 5xx errors over 30 days, 10min freshness for repo updates, WCAG 2.2 AA compliance  
**Scale/Scope**: Multi-tenant, concurrent repository analysis, provider-agnostic LLM integration

## Constitution Check
*GATE: Must pass before Phase 0 research. Re-check after Phase 1 design.*

### I. Code Quality and Readability
- ✅ **PASS**: Pydantic models enforce type hints and meaningful naming
- ✅ **PASS**: FastAPI provides automatic API documentation and validation
- ✅ **PASS**: Async/await patterns ensure explicit I/O handling
- ✅ **PASS**: LangGraph workflow structure promotes maintainable code organization

### II. Testing Discipline and Standards
- ✅ **PASS**: TDD approach with contract tests for GitHub/Bitbucket/LLM provider integrations
- ✅ **PASS**: Coverage targets ≥85% lines/branches, ≥95% for core analysis modules
- ✅ **PASS**: Integration tests for chat flows, repository sync, permission checks
- ✅ **PASS**: Deterministic tests with network/time mocking

### III. UX Consistency and Accessibility
- ✅ **PASS**: Headless API design allows consistent frontend implementation
- ✅ **PASS**: Provenance tracking (repo, path, commit SHA) built into data models
- ✅ **PASS**: Streaming chat responses with proper error states
- ✅ **PASS**: WCAG 2.2 AA compliance enforced in requirements

### IV. Performance and Reliability
- ✅ **PASS**: Performance targets aligned (p50 ≤ 500ms, p95 ≤ 1500ms)
- ✅ **PASS**: Retry logic and circuit breakers for external calls
- ✅ **PASS**: Caching strategy for embeddings and metadata
- ✅ **PASS**: Observability with structured logging and correlation IDs

### V. Data Freshness and Provenance Integrity
- ✅ **PASS**: 10-minute freshness target for repository updates
- ✅ **PASS**: Commit SHA tracking and source attribution required
- ✅ **PASS**: Version-aware indexing (branch, tag, commit)
- ✅ **PASS**: Privacy enforcement with environment-specific access controls

### Technology Stack Requirements
- ✅ **PASS**: FastAPI for REST API endpoints
- ✅ **PASS**: LangChain for LLM integrations and LangGraph for workflows
- ✅ **PASS**: Pydantic for data models and validation
- ✅ **PASS**: Async processing for all I/O operations

### Environment Configuration Standards
- ✅ **PASS**: Development: Local filesystem + MongoDB
- ✅ **PASS**: Production: AWS S3 + MongoDB
- ✅ **PASS**: Environment-specific storage adapters
- ✅ **PASS**: Backup and migration strategies included

## Project Structure

### Documentation (this feature)
```
specs/[###-feature]/
├── plan.md              # This file (/plan command output)
├── research.md          # Phase 0 output (/plan command)
├── data-model.md        # Phase 1 output (/plan command)
├── quickstart.md        # Phase 1 output (/plan command)
├── contracts/           # Phase 1 output (/plan command)
└── tasks.md             # Phase 2 output (/tasks command - NOT created by /plan)
```

### Source Code (repository root)
```
# Option 1: Single project (DEFAULT)
src/
├── agents/
├── models/
├── prompts/
├── services/
├── api/
└── utils/


tests/
├── contract/
├── integration/
└── unit/

# Option 2: Web application (when "frontend" + "backend" detected)
backend/
├── src/
│   ├── agents/
│   ├── models/
│   ├── prompts/
│   ├── services/
│   ├── utils/
│   └── api/
└── tests/

frontend/
├── src/
│   ├── components/
│   ├── pages/
│   └── services/
└── tests/

# Option 3: Mobile + API (when "iOS/Android" detected)
api/
└── [same as backend above]

ios/ or android/
└── [platform-specific structure]
```

**Structure Decision**: Option 1 (Single project) - Headless API service with no frontend components

## Phase 0: Outline & Research
1. **Extract unknowns from Technical Context** above:
   - For each NEEDS CLARIFICATION → research task
   - For each dependency → best practices task
   - For each integration → patterns task

2. **Generate and dispatch research agents**:
   ```
   For each unknown in Technical Context:
     Task: "Research {unknown} for {feature context}"
   For each technology choice:
     Task: "Find best practices for {tech} in {domain}"
   ```

3. **Consolidate findings** in `research.md` using format:
   - Decision: [what was chosen]
   - Rationale: [why chosen]
   - Alternatives considered: [what else evaluated]

**Output**: research.md with all NEEDS CLARIFICATION resolved

## Phase 1: Design & Contracts
*Prerequisites: research.md complete*

1. **Extract entities from feature spec** → `data-model.md`:
   - Entity name, fields, relationships
   - Validation rules from requirements
   - State transitions if applicable

2. **Generate API contracts** from functional requirements:
   - For each user action → endpoint
   - Use standard REST/GraphQL patterns
   - Output OpenAPI/GraphQL schema to `/contracts/`

3. **Generate contract tests** from contracts:
   - One test file per endpoint
   - Assert request/response schemas
   - Tests must fail (no implementation yet)

4. **Extract test scenarios** from user stories:
   - Each story → integration test scenario
   - Quickstart test = story validation steps

5. **Update agent file incrementally** (O(1) operation):
   - Run `.specify/scripts/bash/update-agent-context.sh cursor`
     **IMPORTANT**: Execute it exactly as specified above. Do not add or remove any arguments.
   - If exists: Add only NEW tech from current plan
   - Preserve manual additions between markers
   - Update recent changes (keep last 3)
   - Keep under 150 lines for token efficiency
   - Output to repository root

**Output**: data-model.md, /contracts/*, failing tests, quickstart.md, agent-specific file

## Phase 2: Task Planning Approach
*This section describes what the /tasks command will do - DO NOT execute during /plan*

**Task Generation Strategy**:
- Load `.specify/templates/tasks-template.md` as base
- Generate tasks from Phase 1 design docs (contracts, data model, quickstart)
- Repository API contract → 8 contract test tasks [P] (includes webhook endpoints)
- Wiki API contract → 3 contract test tasks [P] (simplified, no graph endpoints)
- Chat API contract → 6 contract test tasks [P]
- Enhanced Repository model with webhook fields → Pydantic model task [P]
- CodeDocument, WikiStructure, WikiPageDetail, WikiSection → Pydantic model tasks [P]
- Webhook processing (GitHub/Bitbucket signature validation, payload parsing) → implementation tasks
- Semantic search and RAG components → implementation tasks
- LangGraph workflow → simplified agent implementation (no graph analysis)
- Storage adapters → environment-specific implementation tasks
- ECS deployment configuration and Docker setup
- Integration tests from quickstart scenarios including webhook flows

**Ordering Strategy**:
- TDD order: Contract tests → Model tests → Agent tests → Implementation
- Dependency order: Models → Tools → Agents → API endpoints
- Storage adapters in parallel with models [P]
- LangGraph workflows after tools are available
- Mark [P] for parallel execution (independent files/modules)

**Estimated Output**: 42-47 numbered, ordered tasks in tasks.md

**Key Task Categories**:
1. **Setup & Configuration** (6 tasks): Project structure, dependencies, environment config, ECS setup
2. **Contract Tests** (17 tasks): API endpoint contract validation including webhook endpoints
3. **Data Models** (8 tasks): Enhanced Repository model + CodeDocument, WikiStructure, WikiPageDetail, WikiSection
4. **Storage Layer** (6 tasks): Adapters for local/S3 and MongoDB with vector search
5. **Webhook Processing** (4 tasks): GitHub/Bitbucket webhook validation, signature verification, payload parsing
6. **Semantic Search & RAG** (5 tasks): Document processing, embedding generation, similarity search, context retrieval
7. **LangGraph Workflows** (3 tasks): Document processing agent, wiki generation agent, orchestration
8. **API Implementation** (9 tasks): FastAPI endpoints including webhook endpoints
9. **Integration Tests** (8 tasks): End-to-end workflow validation including webhook scenarios

**IMPORTANT**: This phase is executed by the /tasks command, NOT by /plan

## Phase 3+: Future Implementation
*These phases are beyond the scope of the /plan command*

**Phase 3**: Task execution (/tasks command creates tasks.md)  
**Phase 4**: Implementation (execute tasks.md following constitutional principles)  
**Phase 5**: Validation (run tests, execute quickstart.md, performance validation)

## Complexity Tracking
*Fill ONLY if Constitution Check has violations that must be justified*

| Violation | Why Needed | Simpler Alternative Rejected Because |
|-----------|------------|-------------------------------------|
| [e.g., 4th project] | [current need] | [why 3 projects insufficient] |
| [e.g., Repository pattern] | [specific problem] | [why direct DB access insufficient] |


## Progress Tracking
*This checklist is updated during execution flow*

**Phase Status**:
- [x] Phase 0: Research complete (/plan command)
- [x] Phase 1: Design complete (/plan command)
- [x] Phase 2: Task planning complete (/plan command - describe approach only)
- [ ] Phase 3: Tasks generated (/tasks command)
- [ ] Phase 4: Implementation complete
- [ ] Phase 5: Validation passed

**Gate Status**:
- [x] Initial Constitution Check: PASS
- [x] Post-Design Constitution Check: PASS
- [x] All NEEDS CLARIFICATION resolved
- [x] Complexity deviations documented (none required)

---
*Based on Constitution v1.1.0 - See `/memory/constitution.md`*
